import { useCallback, useRef } from 'react';
import { useConfig } from '../ConfigContext';
import type { Config, Model } from 'src/types/config';
import { normalizeApiEndpoint } from 'src/background/util';

const HOST_OLLAMA = 'ollama';

const fetchDataSilently = async (url: string, ModelSettingsPanel = {}) => {
  try {
    const res = await fetch(url, ModelSettingsPanel);
    if (!res.ok) {
      console.error(`[fetchDataSilently] HTTP error! Status: ${res.status} for URL: ${url}`);
      return undefined;
    }
    const data = await res.json();
    return data;
  } catch (error) {
    console.error(`[fetchDataSilently] Fetch or JSON parse error for URL: ${url}`, error);
    return undefined;
  }
};

interface ServiceConfig {
  host: string;
  isEnabled: (config: Config) => boolean;
  getUrl: (config: Config) => string | null;
  getFetchOptions?: (config: Config) => RequestInit | undefined;
  parseFn: (data: any, host: string) => Model[];
  onFetchFail?: (config: Config, updateConfig: (updates: Partial<Config>) => void) => void;
}


export const useUpdateModels = () => {
  const { config, updateConfig } = useConfig();

  const FETCH_INTERVAL =  30 * 1000;
  const lastFetchRef = useRef(0);

  const serviceConfigs: ServiceConfig[] = [
    {
      host: HOST_OLLAMA,
      isEnabled: (cfg) => !!cfg.ollamaUrl && cfg.ollamaConnected === true,
      getUrl: (cfg) => `${cfg.ollamaUrl}/api/tags`,
      parseFn: (data, host) => (data?.models as Model[] ?? []).map(m => ({ ...m, id: m.id ?? m.name, host })),
      onFetchFail: (_, updateCfg) => updateCfg({ ollamaConnected: false, ollamaUrl: '' }),
    },
  ];

  const fetchAllModels = useCallback(async () => {
    const now = Date.now();
    if (now - lastFetchRef.current < FETCH_INTERVAL) {
      
      return;
    }
    lastFetchRef.current = now;

    const currentConfig = config;
    if (!currentConfig) {
      console.warn('[useUpdateModels] Config not available, skipping fetch.');
      return;
    }

    

    const results = await Promise.allSettled(
      serviceConfigs.map(async (service) => {
        if (!service.isEnabled(currentConfig)) {
          return { host: service.host, models: [], status: 'disabled' as const };
        }

        const url = service.getUrl(currentConfig);
        if (!url) {
          console.warn(`[useUpdateModels] Could not determine URL for host: ${service.host}`);
          return { host: service.host, models: [], status: 'error' as const, error: 'Invalid URL' };
        }

        const fetchOptions = service.getFetchOptions ? service.getFetchOptions(currentConfig) : {};
        const data = await fetchDataSilently(url, fetchOptions);

        if (data) {
          const parsedModels = service.parseFn(data, service.host);
          return { host: service.host, models: parsedModels, status: 'success' as const };
        } else {
          if (service.onFetchFail) {
            service.onFetchFail(currentConfig, updateConfig);
          }
          return { host: service.host, models: [], status: 'error' as const, error: 'Fetch failed' };
        }
      })
    );

    let newOverallModels: Model[] = [];
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value.status === 'success') {
        newOverallModels.push(...result.value.models);
      }
    });

    const originalConfigModels = currentConfig.models || [];

    const haveModelsChanged = (newModelsList: Model[], existingModelsList: Model[]) => {
      if (newModelsList.length !== existingModelsList.length) return true;
      const sortById = (a: Model, b: Model) => a.id.localeCompare(b.id);
      const sortedNew = [...newModelsList].sort(sortById);
      const sortedExisting = [...existingModelsList].sort(sortById);
      return JSON.stringify(sortedNew) !== JSON.stringify(sortedExisting);
    };

    const pendingConfigUpdates: Partial<Config> = {};

    if (haveModelsChanged(newOverallModels, originalConfigModels)) {
      
      pendingConfigUpdates.models = newOverallModels;
    }

    const currentSelectedModel = currentConfig.selectedModel;
    const finalModelsForSelection = pendingConfigUpdates.models || originalConfigModels;

    const isSelectedStillAvailable = currentSelectedModel &&
      finalModelsForSelection.some(m => m.id === currentSelectedModel);

    const newSelectedModel = isSelectedStillAvailable ? currentSelectedModel : finalModelsForSelection[0]?.id;

    if (newSelectedModel !== currentSelectedModel || pendingConfigUpdates.models) {
        pendingConfigUpdates.selectedModel = newSelectedModel;
    }

    if (Object.keys(pendingConfigUpdates).length > 0) {
      updateConfig(pendingConfigUpdates);
    } else {
      
    }

    
  }, [config, updateConfig, FETCH_INTERVAL, serviceConfigs]);

  return { fetchAllModels };
};
