// Test script for title generation functions
// This is a simple test to verify the title generation logic works correctly

// Helper function to create fallback title from user message
const createFallbackTitle = (userMessage) => {
  // Remove common question words and phrases
  const cleanedMessage = userMessage
    .replace(/^(what\s+(is|are|was|were|do|does|did|can|could|would|will|should)\s+)/i, '')
    .replace(/^(how\s+(do|does|did|can|could|would|will|should|to)\s+)/i, '')
    .replace(/^(can\s+you\s+)/i, '')
    .replace(/^(could\s+you\s+)/i, '')
    .replace(/^(would\s+you\s+)/i, '')
    .replace(/^(please\s+)/i, '')
    .replace(/^(help\s+me\s+)/i, '')
    .replace(/^(tell\s+me\s+)/i, '')
    .replace(/^(explain\s+)/i, '')
    .replace(/^(show\s+me\s+)/i, '')
    .replace(/\?+$/, '') // Remove trailing question marks
    .trim();

  // Take first 3-6 meaningful words
  const words = cleanedMessage.split(/\s+/).filter(word => word.length > 2);
  const titleWords = words.slice(0, Math.min(6, Math.max(3, words.length)));
  
  // Capitalize first letter of each word
  return titleWords
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

// Enhanced helper function to extract and clean title from AI response
const extractTitle = (response, fallbackMessage = '') => {
  // First remove any thinking blocks and clean the response
  let titleOnly = response
    .replace(/<think>[\s\S]*?<\/think>/g, '') // Remove thinking blocks
    .replace(/["'`]/g, '') // Remove quotes
    .replace(/#/g, '') // Remove hashtags
    .replace(/^\s*title:\s*/i, '') // Remove "title:" prefix if present
    .replace(/^\s*-\s*/, '') // Remove leading dash
    .trim();

  // If the response is empty or too short, use fallback
  if (!titleOnly || titleOnly.length < 3) {
    return fallbackMessage ? createFallbackTitle(fallbackMessage) : 'New Chat';
  }

  // Split into words and filter out very short words and common filler words
  const words = titleOnly.split(/\s+/).filter(word => {
    const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
    return cleanWord.length > 1 && 
           !['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'].includes(cleanWord);
  });

  // Take 3-6 words for the title
  const titleWords = words.slice(0, Math.min(6, Math.max(3, words.length)));
  
  // Capitalize appropriately
  return titleWords
    .map((word, index) => {
      const cleanWord = word.replace(/[^\w]/g, '');
      // Capitalize first word and important words, keep short words lowercase unless first
      if (index === 0 || cleanWord.length > 3) {
        return cleanWord.charAt(0).toUpperCase() + cleanWord.slice(1).toLowerCase();
      }
      return cleanWord.toLowerCase();
    })
    .join(' ');
};

// Test cases
const testCases = [
  {
    input: "What is the reason for the current riots in New York?",
    expected: "Reason Current Riots New York"
  },
  {
    input: "How do I install Docker on Ubuntu?",
    expected: "Install Docker Ubuntu"
  },
  {
    input: "Can you explain quantum computing?",
    expected: "Explain Quantum Computing"
  },
  {
    input: "Please help me debug this Python code",
    expected: "Debug This Python Code"
  },
  {
    input: "Tell me about machine learning algorithms",
    expected: "About Machine Learning Algorithms"
  },
  {
    input: "What are the best restaurants in Paris?",
    expected: "Best Restaurants Paris"
  }
];

testCases.forEach((testCase, index) => {
  const result = createFallbackTitle(testCase.input);
});

// Test AI response extraction

const aiResponses = [
  'New York Riots Reason',
  '"Install Docker Ubuntu"',
  'Title: Quantum Computing Explanation',
  '- Python Code Debugging',
  '<think>Let me think about this...</think>Machine Learning Algorithms'
];

aiResponses.forEach((response, index) => {
  const result = extractTitle(response, "fallback message");
});


