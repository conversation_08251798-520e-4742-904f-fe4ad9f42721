// Dynamic PDF.js loader to reduce initial bundle size
let pdfjsLib: any = null;
let isLoading = false;
let loadPromise: Promise<any> | null = null;

export async function loadPdfJs() {
  if (pdfjsLib) {
    return pdfjsLib;
  }

  if (isLoading && loadPromise) {
    return loadPromise;
  }

  isLoading = true;
  loadPromise = (async () => {
    try {
      // Dynamic import to load PDF.js only when needed
      const pdfjs = await import('pdfjs-dist');
      
      // Set up worker
      try {
        // Try to load worker from extension
        const workerUrl = chrome.runtime.getURL('pdf.worker.mjs');
        if (workerUrl) {
          pdfjs.GlobalWorkerOptions.workerSrc = workerUrl;
        } else {
          // Fallback to CDN worker
          pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
        }
      } catch (e) {
        console.warn("Could not set up PDF worker from extension, using CDN fallback:", e);
        // Fallback to CDN worker
        pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
      }

      pdfjsLib = pdfjs;
      return pdfjs;
    } catch (error) {
      console.error("Failed to load PDF.js:", error);
      throw error;
    } finally {
      isLoading = false;
    }
  })();

  return loadPromise;
}

export async function extractTextFromPdf(pdfUrl: string, callId?: number): Promise<string> {
  try {
    const pdfjs = await loadPdfJs();
    
    const response = await fetch(pdfUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
    }
    const arrayBuffer = await response.arrayBuffer();
    
    const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
    
    let fullText = '';
    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const pageText = textContent.items.map((item: any) => ('str' in item ? item.str : '')).join(' ');
      fullText += pageText + '\n\n';
      
      // Progress logging for large PDFs
      if (i % 10 === 0 || i === pdf.numPages) {
        console.log(`[${callId || 'PDF'}] Processed ${i}/${pdf.numPages} pages`);
      }
    }
    
    return fullText.trim();
  } catch (error) {
    console.error(`[${callId || 'PDF'}] Error extracting text from PDF (${pdfUrl}):`, error);
    throw error;
  }
}
