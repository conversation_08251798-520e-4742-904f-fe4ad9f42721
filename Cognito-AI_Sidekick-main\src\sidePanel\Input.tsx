import type { FC } from 'react';
import { useEffect, useRef, useState, Dispatch, SetStateAction, MouseEvent } from 'react';
import { BsSend, BsStopCircle } from "react-icons/bs";
import { useConfig } from './ConfigContext';
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/src/background/util";


interface InputProps {
    isLoading: boolean;
    message: string;
    setMessage: Dispatch<SetStateAction<string>>; 
    onSend: () => void;
    onStopRequest: () => void;
}

export const Input: FC<InputProps> = ({ isLoading, message, setMessage, onSend, onStopRequest }) => {
  const { config } = useConfig();
  const ref = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    ref.current?.focus();
  }, [message, config?.chatMode]);

  let placeholderText = 'Search the web with AI...';
  
    const handleSendClick = () => {
    if (isLoading) {
      onStopRequest();
    } else {
      if (message.trim()) {
        onSend();
      }
    }
  };

  const handleTextareaKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (isLoading) return;
    if (event.key === 'Enter' && message.trim() && !event.altKey && !event.metaKey && !event.shiftKey) {
      event.preventDefault();
      event.stopPropagation();
      onSend();
    }
  };

  return (
    <div className="flex flex-col gap-3 mb-4">
      <div className={cn(
        "flex w-full border border-border items-center gap-2 p-3 bg-card rounded-xl shadow-sm",
        isFocused && "ring-2 ring-primary/20 border-primary/50"
      )}>
        <Textarea
          autosize
          ref={ref}
          minRows={1}
          maxRows={8}
          autoComplete="off"
          id="user-input"
          placeholder={placeholderText}
          value={message}
          autoFocus
          onChange={event => setMessage(event.target.value)}
          onKeyDown={handleTextareaKeyDown}
          className="flex-grow bg-transparent border-none shadow-none outline-none focus-visible:ring-0 resize-none text-apple-body"
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />

        <div className="flex items-center gap-2">
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  aria-label="Send"
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "p-2 rounded-lg h-8 w-8 flex items-center justify-center",
                    isLoading ? "text-destructive hover:bg-destructive/10" : "text-primary hover:bg-primary/10",
                    !isLoading && !message.trim() && "opacity-50"
                  )}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => { e.stopPropagation(); handleSendClick();}}
                  disabled={!isLoading && !message.trim()}
                >
                  {isLoading ? (
                    <BsStopCircle className="h-4 w-4" />
                  ) : (
                    <BsSend className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top" className="bg-popover text-popover-foreground border border-border">
                <p className="text-apple-footnote">{isLoading ? "Stop" : "Send"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};