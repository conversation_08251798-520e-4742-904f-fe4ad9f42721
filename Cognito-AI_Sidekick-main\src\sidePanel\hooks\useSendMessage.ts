import { Dispatch, SetStateAction, useRef } from 'react';
import { MessageTurn } from '../ChatHistory';
import { fetchDataAsStream, webSearch, processQueryWithAI, scrapeUrlContent } from '../network';
import storage from 'src/background/storageUtil';
import type { Config, Model } from 'src/types/config';
import { normalizeApiEndpoint } from 'src/background/util';
import { handleHighCompute, handleMediumCompute } from './computeHandlers';
import { ChatMode, ChatStatus } from '../../types/config';
import { extractTextFromPdf } from '../utils/pdfLoader';

interface ApiMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export const getAuthHeader = (config: Config, currentModel: Model) => {
  // Only Ollama is supported, which doesn't require auth headers
  return undefined;
};

const useSendMessage = (
  isLoading: boolean,
  originalMessage: string,
  currentTurns: MessageTurn[],
  _webContent: string, 
  config: Config | null | undefined,
  setTurns: Dispatch<SetStateAction<MessageTurn[]>>,
  setMessage: Dispatch<SetStateAction<string>>,
  setWebContent: Dispatch<SetStateAction<string>>,
  setPageContent: Dispatch<SetStateAction<string>>,
  setLoading: Dispatch<SetStateAction<boolean>>,
  setChatStatus: Dispatch<SetStateAction<ChatStatus>>
) => {
  const completionGuard = useRef<number | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const updateAssistantTurn = (callId: number | null, update: string, isFinished: boolean, isError?: boolean, isCancelled?: boolean) => {
    if (completionGuard.current !== callId && !isFinished && !isError && !isCancelled) {
      
      return;
    }
    if (completionGuard.current === null && callId !== null) {
        if ((update === "" && isFinished && !isError && !isCancelled) ||
            (isError && (update.includes("Operation cancelled by user") || update.includes("Streaming operation cancelled")))) {
            
            setLoading(false);
            setChatStatus('idle');
            return;
        }
    }

    setTurns(prevTurns => {
      if (prevTurns.length === 0 || prevTurns[prevTurns.length - 1].role !== 'assistant') {
        console.warn(`[${callId}] updateAssistantTurn: No assistant turn found or last turn is not assistant.`);
        if (isError) {
          const errorTurn: MessageTurn = {
            role: 'assistant',
            rawContent: `Error: ${update || 'Unknown operation error'}`,
            status: 'error',
            timestamp: Date.now(),
          };
          return [...prevTurns, errorTurn];
        }
        return prevTurns;
      }
      const lastTurn = prevTurns[prevTurns.length - 1];
      
      const updatedStatus = (isError === true) ? 'error' : (isCancelled === true) ? 'cancelled' : (isFinished ? 'complete' : 'streaming');
      let finalContentForTurn: string;
      
      if (isCancelled) {
        const existingContent = lastTurn.rawContent || "";
        finalContentForTurn = existingContent + (existingContent ? " " : "") + update;
      } else if (isError) {
        finalContentForTurn = `Error: ${update || 'Unknown stream/handler error'}`;
      } else {
        finalContentForTurn = update; 
      }
      
      return [...prevTurns.slice(0, -1), { ...lastTurn, rawContent: finalContentForTurn, status: updatedStatus, timestamp: Date.now() }];
    });

    if (isFinished || (isError === true) || (isCancelled === true)) {
      
      setLoading(false);
      setChatStatus(isError ? 'idle' : isCancelled ? 'idle' : 'done');

      if (completionGuard.current === callId) {
        completionGuard.current = null;
        if (abortControllerRef.current) {
            abortControllerRef.current = null;
        }
      }
    }
  };

  const onSend = async (overridedMessage?: string) => {
    const callId = Date.now();
    
    
    const message = overridedMessage || "";

    if (!config) {
      
      setLoading(false);
      return;
    }
    if (!message || !config) {
      
      return;
    }

    if (completionGuard.current !== null) {
      console.warn(`[${callId}] useSendMessage: Another send operation (ID: ${completionGuard.current}) is already in progress. Aborting previous.`);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    }

    const controller = new AbortController();
    abortControllerRef.current = controller;

    
    setLoading(true);
    setWebContent('');
    setPageContent('');

    const currentChatMode = config.chatMode as ChatMode || 'chat';
    if (currentChatMode === 'web') {
      setChatStatus('searching');
    } else if (currentChatMode === 'page') {
      setChatStatus('reading');
    } else {
      setChatStatus('thinking');
    }

    completionGuard.current = callId;

    // --- URL Detection and Scraping ---
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const urls = message.match(urlRegex);
    let scrapedContent = '';
    if (urls && urls.length > 0) {
      setChatStatus('searching');
      try {
        const scrapedResults = await Promise.all(
          urls.map(url => scrapeUrlContent(url, controller.signal))
        );
        scrapedContent = scrapedResults
          .map((content, idx) => `Content from [${urls[idx]}]:\n${content}`)
          .join('\n\n');
      } catch (e) {
        scrapedContent = '[Error scraping one or more URLs]';
      }
      setChatStatus('thinking');
    }

    const userTurn: MessageTurn = {
      role: 'user',
      status: 'complete',
      rawContent: message,
      timestamp: Date.now()
    };
    setTurns(prevTurns => [...prevTurns, userTurn]);
    setMessage('');
    

    const assistantTurnPlaceholder: MessageTurn = {
        role: 'assistant',
        rawContent: '',
        status: 'streaming',
        timestamp: Date.now() + 1 
    };
    setTurns(prevTurns => [...prevTurns, assistantTurnPlaceholder]);
    

    let queryForProcessing = message;
    let searchRes: string = '';
    let processedQueryDisplay = '';

    const performSearch = true; // Always perform web search for Chromepanion search assistant
    const currentModel = config?.models?.find(m => m.id === config.selectedModel);
    if (!currentModel) {
      console.error(`[${callId}] useSendMessage: No current model found.`);
      updateAssistantTurn(callId, "Configuration error: No model selected.", true, true);
      return;
    }
    const authHeader = getAuthHeader(config, currentModel);

    if (performSearch) {
      
      setChatStatus('thinking');    
      const historyForQueryOptimization: ApiMessage[] = currentTurns.map(turn => ({
        role: turn.role,
        content: turn.rawContent
      }));
      try {
        const optimizedQuery = await processQueryWithAI(
          message,
          config,
          currentModel,
          authHeader, 
          controller.signal,
          historyForQueryOptimization
        );
        if (optimizedQuery && optimizedQuery.trim() && optimizedQuery !== message) {
          queryForProcessing = optimizedQuery;
          processedQueryDisplay = `**Optimized query:** "*${queryForProcessing}*"\n\n`;
          
        } else {
          processedQueryDisplay = `**Original query:** "${queryForProcessing}"\n\n`;
          
        }
      } catch (optError) {
        console.error(`[${callId}] Query optimization failed:`, optError);
        processedQueryDisplay = `**Fallback query:** "${queryForProcessing}"\n\n`;
      }
    } else {
      queryForProcessing = message;
    }

    if (performSearch) {
      
      setChatStatus('searching');

      try {
        searchRes = await webSearch(queryForProcessing, config, controller.signal);
        setChatStatus('thinking');     
        if (controller.signal.aborted) {
          
          return;
        }
      } catch (searchError: any) {
        console.error(`[${callId}] Web search failed:`, searchError);
        if (searchError.name === 'AbortError' || controller.signal.aborted) {
          
          return;
        } else {
          searchRes = '';
          const errorMessage = `Web Search Failed: ${searchError instanceof Error ? searchError.message : String(searchError)}`;
          setChatStatus('idle');     
          updateAssistantTurn(callId, errorMessage, true, true, false);
          return;
        }
      }
      
      if (processedQueryDisplay) { 
        setTurns(prevTurns => prevTurns.map(t => (t.role === 'assistant' && prevTurns[prevTurns.length -1] === t && t.status !== 'complete' && t.status !== 'error' && t.status !== 'cancelled') ? { ...t, webDisplayContent: processedQueryDisplay } : t));
      }
    }
    
    const messageToUse = performSearch ? queryForProcessing : message;
    const webLimit = 1000 * (config?.webLimit || 1);
    const limitedWebResult = webLimit && typeof searchRes === 'string'
      ? searchRes.substring(0, webLimit)
      : searchRes;
    const webContentForLlm = config?.webLimit === 128 ? searchRes : limitedWebResult;

    const messageForApi: ApiMessage[] = currentTurns
      .map((turn): ApiMessage => ({
        content: turn.rawContent || '',
        role: turn.role
      }))
      .concat({ role: 'user', content: message });

    let pageContentForLlm = '';
    if (config?.chatMode === 'page') {
      let currentPageContent = '';
      
      setChatStatus('reading');      
      try {
        const [tab] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
        
        if (tab?.url && !tab.url.startsWith('chrome://')) {
          const tabUrl = tab.url;
          const tabMimeType = (tab as chrome.tabs.Tab & { mimeType?: string }).mimeType;
          const isPdfUrl = tabUrl.toLowerCase().endsWith('.pdf') ||
                           (tabMimeType && tabMimeType === 'application/pdf');

          if (isPdfUrl) {
            
            try {
              currentPageContent = await extractTextFromPdf(tabUrl, callId);
              
            } catch (pdfError) {
              console.error(`[${callId}] Failed to extract text from PDF ${tabUrl}:`, pdfError);
              currentPageContent = `Error extracting PDF content: ${pdfError instanceof Error ? pdfError.message : "Unknown PDF error"}. Falling back.`;
            }
          } else {
            
            const storedPageString = await storage.getItem('pagestring');
            currentPageContent = storedPageString || '';
            
          }
        } else {
          
        }
      } catch (pageError) {
        console.error(`[${callId}] Error getting active tab or initial page processing:`, pageError);
        currentPageContent = `Error accessing page content: ${pageError instanceof Error ? pageError.message : "Unknown error"}`;
      }

      const charLimit = 1000 * (config?.contextLimit || 1);
      const safeCurrentPageContent = typeof currentPageContent === 'string' ? currentPageContent : '';
      const limitedContent = charLimit && safeCurrentPageContent
        ? safeCurrentPageContent.substring(0, charLimit)
        : safeCurrentPageContent;
      pageContentForLlm = config?.contextLimit === 128 ? safeCurrentPageContent : limitedContent;
      setPageContent(pageContentForLlm || '');
      setChatStatus('thinking');     
      
    } else {
      setPageContent('');
    }

    const persona = config?.personas?.[config?.persona] || '';
    const pageContextString = (config?.chatMode === 'page' && pageContentForLlm)
      ? `Use the following page content for context: ${pageContentForLlm}`
      : '';
    const webContextString = webContentForLlm
      ? `Refer to this web search summary: ${webContentForLlm}`
      : '';

    
    let userContextStatement = '';
    const userName = config.userName?.trim();
    const userProfile = config.userProfile?.trim();

    if (userName && userName.toLowerCase() !== 'user' && userName !== '') {
      userContextStatement = `You are interacting with a user named "${userName}".`;
      if (userProfile) {
        userContextStatement += ` Their provided profile information is: "${userProfile}".`;
      }
    } else if (userProfile) {
      userContextStatement = `You are interacting with a user. Their provided profile information is: "${userProfile}".`;
    }

    const systemPromptParts = [];
    if (persona) systemPromptParts.push(persona);
    if (userContextStatement) systemPromptParts.push(userContextStatement);
    if (pageContextString) systemPromptParts.push(pageContextString);
    if (webContextString) systemPromptParts.push(webContextString);
    if (scrapedContent) systemPromptParts.push(`Use the following scraped content from URLs in the user's message:\n${scrapedContent}`);

    const systemContent = systemPromptParts.join('\n\n').trim();

    

    // --- Step 4: Execute based on Compute Level ---
    try {
      setChatStatus('thinking'); 
      if (config?.computeLevel === 'high' && currentModel) {
        
        await handleHighCompute(
          messageToUse,
          currentTurns,
          config,
          currentModel,
          authHeader,
          (update, isFinished) => updateAssistantTurn(callId, update, Boolean(isFinished)),
          controller.signal
        );
        
      } else if (config?.computeLevel === 'medium' && currentModel) {
        
        await handleMediumCompute(
          messageToUse,
          currentTurns,
          config,
          currentModel,
          authHeader,
          (update, isFinished) => updateAssistantTurn(callId, update, Boolean(isFinished)),
          controller.signal
        );
        
      } else {
        
        const configBody = { stream: true };
        const urlMap: Record<string, string> = {
          ollama: `${config?.ollamaUrl || ''}/api/chat`,
        };
        const host = currentModel.host || '';
        const url = urlMap[host];

        if (!url) {
          updateAssistantTurn(callId, `Configuration error: Could not determine API URL for host '${currentModel.host}'.`, true, true);
          return;
        }
        const messagesForApiPayload: ApiMessage[] = [];
        if (systemContent.trim() !== '') {
          messagesForApiPayload.push({ role: 'system', content: systemContent });
        }
        messagesForApiPayload.push(...messageForApi);

        
        await fetchDataAsStream(
          url,
          {
            ...configBody,
            model: config?.selectedModel || '',
            messages: messagesForApiPayload,
            temperature: config?.temperature ?? 0.7,
            max_tokens: config?.maxTokens ?? 32048,
            top_p: config?.topP ?? 1,
            presence_penalty: config?.presencepenalty ?? 0,
          },
          (part: string, isFinished?: boolean, isError?: boolean) => {
            updateAssistantTurn(callId, part, Boolean(isFinished), Boolean(isError));
            if (isFinished || isError) {
              
            }
          },
          authHeader,
          currentModel.host || '',
          controller.signal
        );
        
      }
    } catch (error) {
      if (controller.signal.aborted) {
        
        if (isLoading) setLoading(false);
        setChatStatus('idle');
        if (completionGuard.current === callId) {
            completionGuard.current = null;
        }
        if (abortControllerRef.current && abortControllerRef.current.signal === controller.signal) {
            abortControllerRef.current = null;
        }
      } else {
        console.error(`[${callId}] useSendMessage: Error during send operation:`, error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        updateAssistantTurn(callId, errorMessage, true, true);
      }
    }
    
  };

  const onStop = () => {
    const currentCallId = completionGuard.current;
    if (currentCallId !== null) {
      
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
      updateAssistantTurn(currentCallId, "[Operation cancelled by user]", true, false, true);
    } else {
      
      setLoading(false); 
      setChatStatus('idle'); 
    }
  };
  return { onSend, onStop };
}

export default useSendMessage;