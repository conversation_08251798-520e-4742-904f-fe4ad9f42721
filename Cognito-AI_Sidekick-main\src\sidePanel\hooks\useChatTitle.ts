import { useEffect, useState, useRef } from 'react';

import { normalizeApiEndpoint } from 'src/background/util';
import { useConfig } from '../ConfigContext';
import { fetchDataAsStream } from '../network';
import { MessageTurn } from '../ChatHistory';

interface ApiMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// Helper function to create fallback title from user message
const createFallbackTitle = (userMessage: string): string => {
  // Remove common question words and phrases
  const cleanedMessage = userMessage
    .replace(/^(what\s+(is|are|was|were|do|does|did|can|could|would|will|should)\s+)/i, '')
    .replace(/^(how\s+(do|does|did|can|could|would|will|should|to)\s+)/i, '')
    .replace(/^(can\s+you\s+)/i, '')
    .replace(/^(could\s+you\s+)/i, '')
    .replace(/^(would\s+you\s+)/i, '')
    .replace(/^(please\s+)/i, '')
    .replace(/^(help\s+me\s+)/i, '')
    .replace(/^(tell\s+me\s+)/i, '')
    .replace(/^(explain\s+)/i, '')
    .replace(/^(show\s+me\s+)/i, '')
    .replace(/\?+$/, '') // Remove trailing question marks
    .trim();

  // Take first 3-6 meaningful words
  const words = cleanedMessage.split(/\s+/).filter(word => word.length > 2);
  const titleWords = words.slice(0, Math.min(6, Math.max(3, words.length)));

  // Capitalize first letter of each word
  return titleWords
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

// Enhanced helper function to extract and clean title from AI response
const extractTitle = (response: string, fallbackMessage: string = ''): string => {
  // First remove any thinking blocks and clean the response
  let titleOnly = response
    .replace(/<think>[\s\S]*?<\/think>/g, '') // Remove thinking blocks
    .replace(/["'`]/g, '') // Remove quotes
    .replace(/#/g, '') // Remove hashtags
    .replace(/^\s*title:\s*/i, '') // Remove "title:" prefix if present
    .replace(/^\s*-\s*/, '') // Remove leading dash
    .trim();

  // If the response is empty or too short, use fallback
  if (!titleOnly || titleOnly.length < 3) {
    return fallbackMessage ? createFallbackTitle(fallbackMessage) : 'New Chat';
  }

  // Split into words and filter out very short words and common filler words
  const words = titleOnly.split(/\s+/).filter(word => {
    const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
    return cleanWord.length > 1 &&
           !['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'].includes(cleanWord);
  });

  // Take 3-6 words for the title
  const titleWords = words.slice(0, Math.min(6, Math.max(3, words.length)));

  // Capitalize appropriately
  return titleWords
    .map((word, index) => {
      const cleanWord = word.replace(/[^\w]/g, '');
      // Capitalize first word and important words, keep short words lowercase unless first
      if (index === 0 || cleanWord.length > 3) {
        return cleanWord.charAt(0).toUpperCase() + cleanWord.slice(1).toLowerCase();
      }
      return cleanWord.toLowerCase();
    })
    .join(' ');
};

export const useChatTitle = (isLoading: boolean, turns: MessageTurn[], message: string) => {
  const [chatTitle, setChatTitle] = useState('');
  const { config } = useConfig();
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    // Simplified conditions:
    // 1. Not already loading
    // 2. Have enough messages to generate a meaningful title
    // 3. No title yet
    // 4. Title generation is enabled in config
    if (!isLoading && 
        turns.length >= 2 && 
        !chatTitle && 
        config?.generateTitle) {

      // If a previous title generation is in progress, abort it
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;
      const currentModel = config?.models?.find((model) => model.id === config.selectedModel);
      if (!currentModel) return;

      // Get the first user message for title generation
      const firstUserMessage = turns.find(turn => turn.role === 'user')?.rawContent || '';

      const messagesForTitle: ApiMessage[] = [
        {
          role: 'system',
          content: `You are a title generator. Create concise, descriptive titles (3-6 words) for chat conversations based on the user's first message.

Rules:
- Remove question words like "what is", "how do", "can you", "please", etc.
- Focus on the main topic or subject
- Use title case (capitalize important words)
- Be specific and searchable
- No quotes, no explanations, just the title

Examples:
"What is the reason for the current riots in New York?" → "New York Riots Reason"
"How do I install Docker on Ubuntu?" → "Install Docker Ubuntu"
"Can you explain quantum computing?" → "Quantum Computing Explanation"
"Please help me debug this Python code" → "Python Code Debugging"
"Tell me about machine learning algorithms" → "Machine Learning Algorithms"
"What are the best restaurants in Paris?" → "Best Paris Restaurants"`
        },
        {
          role: 'user',
          content: `Create a title for this message: "${firstUserMessage}"`
        }
      ];

      const getApiConfig = () => {
        const baseConfig = {
          body: {
            model: currentModel.id,
            messages: messagesForTitle,
            stream: !['ollama'].includes(currentModel.host || '')
          },
          headers: {} as Record<string, string>
        };

        switch (currentModel.host) {
          case 'ollama':
            return {
              ...baseConfig,
              url: `${config.ollamaUrl}/api/chat`
            };
          }
      };

      const apiConfig = getApiConfig();
      
      if (!apiConfig) return;

      const handleFetchError = (err: any) => {
        if (signal.aborted) {
          
        } else {
          console.error('Title generation failed:', err);
        }
      };

      if (['ollama'].includes(currentModel.host || '')) {
        fetch(apiConfig.url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...apiConfig.headers
          },
          body: JSON.stringify(apiConfig.body),
          signal
        })
        .then(res => res.json())
        .then(data => {
          // Fix: Use correct Ollama response format (data.message.content, not data.choices)
          const rawTitle = data.message?.content || '';
          const cleanTitle = extractTitle(rawTitle, firstUserMessage);
          if (cleanTitle && cleanTitle !== 'New Chat') {
            
            setChatTitle(cleanTitle);
          } else {
            // Fallback to creating title from user message
            const fallbackTitle = createFallbackTitle(firstUserMessage);
            
            setChatTitle(fallbackTitle);
          }
        })
        .catch(err => {
          handleFetchError(err);
          // On error, create fallback title from user message
          const fallbackTitle = createFallbackTitle(firstUserMessage);
          
          setChatTitle(fallbackTitle);
        });
      } else {
        let accumulatedTitle = '';
        fetchDataAsStream(
          apiConfig.url,
          apiConfig.body,
          (part: string, isFinished?: boolean) => {
            accumulatedTitle = part;
            if (signal.aborted) {
              
              return;
            }
            if (isFinished) {
              const cleanTitle = extractTitle(accumulatedTitle, firstUserMessage);
              if (cleanTitle && cleanTitle !== 'New Chat') {
                
                setChatTitle(cleanTitle);
              } else {
                // Fallback to creating title from user message
                const fallbackTitle = createFallbackTitle(firstUserMessage);
                
                setChatTitle(fallbackTitle);
              }
            }
          },
          apiConfig.headers,
          currentModel.host || '',
          signal
        ).catch(err => {
          handleFetchError(err);
          // On error, create fallback title from user message
          const fallbackTitle = createFallbackTitle(firstUserMessage);
          
          setChatTitle(fallbackTitle);
        });
      }
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, [isLoading, turns, message, config, chatTitle]);
  
  return { chatTitle, setChatTitle };
};