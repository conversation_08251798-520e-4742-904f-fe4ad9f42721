import type { FC } from 'react';
import { ConnectOllama } from './ConnectOllama';
import { ModelSelector } from './ModelSelector';
import { cn } from "@/src/background/util";

type ConnectionProps = {
  title: string;
  Component: FC<unknown>;
};

const ConnectionSection: FC<ConnectionProps> = ({
  title,
  Component,
}) => (
  <div className="px-6 py-4 border-b border-border/50 last:border-b-0">
    <div className="flex items-center justify-between mb-3">
      <h4 className="text-apple-body font-semibold text-foreground">
        {title}
      </h4>
    </div>
    <Component />
  </div>
);

export const ConnectCard: FC = () => {
  return (
    <div className={cn(
      "settings-card",
      "bg-card border border-border rounded-xl shadow-sm",
      "hover:shadow-md hover:border-border/80",
      "overflow-hidden"
    )}>
      <div className={cn(
        "settings-card-header",
        "px-6 py-4 border-b border-border/50"
      )}>
        <div>
          <h3 className="text-apple-title3 font-semibold text-foreground">API Access</h3>
          <p className="text-apple-footnote text-muted-foreground">Connect to your AI services</p>
        </div>
      </div>

      <div className="divide-y divide-border/50">
        <ConnectionSection Component={ConnectOllama} title="Ollama" />
        <ConnectionSection Component={ModelSelector} title="Model Selection" />
      </div>
    </div>
  );
};
