import { contentLoaded } from 'src/state/slices/content';
import { createStoreProxy } from 'src/state/store';
import ChannelNames from '../types/ChannelNames';
import Defuddle from 'defuddle';
import * as turndownPluginGfm from 'turndown-plugin-gfm';
import TurndownService from 'turndown';

(async () => {
  try {
    if (
      window.location.protocol === 'chrome:' ||
      window.location.protocol === 'chrome-extension:'
    ) {
      return;
    }
    

    const store = createStoreProxy(ChannelNames.ContentPort);
    

    try {
      await store.ready();
      
      store.dispatch(contentLoaded());
      
    } catch (initError) {
      console.error('ChromePanion - Content script store init error:', initError);
    }
  } catch (err) {
    console.error('ChromePanion - Content script main initialization error:', err);
  }
})();

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'DEFUDDLE_PAGE_CONTENT') {
    let turndownService: TurndownService | null = null;

    try {
      if (document.contentType === 'application/pdf') {
        sendResponse({
          success: false,
          error: 'Cannot defuddle PDF content directly. Please save or copy text manually.',
          title: document.title || 'PDF Document'
        });
        return true;
      }

      if (typeof Defuddle === 'undefined') {
        sendResponse({ success: false, error: 'Defuddle library not available.', title: document.title });
        return true;
      }

      const defuddleInstance = new Defuddle(document, {
        markdown: false,
        url: document.location.href
      });
      const defuddleResult = defuddleInstance.parse();

      if (typeof TurndownService === 'undefined') {
        sendResponse({ success: false, error: 'TurndownService library not available.', title: document.title });
        return true;
      }

      turndownService = new TurndownService({ headingStyle: 'atx', codeBlockStyle: 'fenced' })
        .use(turndownPluginGfm.gfm);

      const markdownContent = turndownService.turndown(defuddleResult.content || '');

      const firstHeading = document.querySelector('h1, h2, h3')?.textContent?.trim();
      const fallbackTitle = document.title || 'Untitled Note';

      sendResponse({
        success: true,
        title: firstHeading || defuddleResult.title || fallbackTitle,
        content: markdownContent
     });
    } catch (error: any) {
      sendResponse({ success: false, error: error.message, title: document.title });
    }
    
    return true;
  }

});

export {};